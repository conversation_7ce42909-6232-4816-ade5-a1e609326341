<route lang="json5" type="home">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <view class=""></view>
    </template>
    <view class="flex justify-center items-center">
      <image :src="logoImg" class="logo-img"></image>
    </view>

    <view class="login-input">
      <view class="login-b relative">
        <view class="text-34rpx p-l-10rpx absolute left-10rpx bottom-0rpx">+86</view>
        <wd-input
          v-model="phone"
          :maxlength="11"
          no-border
          placeholder="请输入手机号"
          type="number"
        />
      </view>
      <!-- <view class="login-xy-constr">首次登录将自动注册</view> -->
      <view class="login-xy flex-c p-t-30rpx">
        <wd-checkbox
          v-model="checked"
          :disabled="disabledShow"
          checked-color="#44BCC6"
          placeholderClass="placeholderClass"
          shape="square"
          size="30"
          @click="handleChecked"
        ></wd-checkbox>

        <view class="login-xy-text">
          请阅读并同意
          <text class="c-#0275ff text-24rpx" @click="uesrAgreement">《用户协议》</text>
          <text class="c-#0275ff text-24rpx" @click="privacyAgreement">《隐私协议》</text>
        </view>
      </view>
    </view>

    <template #bottom>
      <view class="login-container m-b-300rpx">
        <view class="login-btn">
          <view class="btn_fixed" @click="setCode">
            <view class="btn_box">
              <view class="btn_bg font-600">手机验证登录</view>
            </view>
          </view>
          <view class="login-text-nomal" @click="accountLogin">账户密码登录</view>
        </view>
        <!-- <view class="login-ohter">
        <wd-divider>其他登录选项</wd-divider>
        <view class="login-ohter-icon">
          <view class="t-icon t-icon-weixin"></view>
          <view class="t-icon t-icon-QQ"></view>
          <view class="t-icon t-icon-zhifubaozhifu"></view>
        </view>
      </view> -->
      </view>
      <common-link v-if="!showAgreement" :fixed="false" class="m-b-50rpx"></common-link>
    </template>

    <!-- <wd-message-box /> -->
    <wd-message-box selector="wd-message-box-slot">
      <view class="text-26rpx flex-c flex-wrap flex-just line-20 dark-color font-500">
        请阅读并同意
      </view>
      <view class="flex-c flex-wrap flex-just line-20 dark-color font-500">
        <view class="color-b text-26rpx font-500" @click="uesrAgreement">《用户协议》</view>

        <view class="color-b text-26rpx font-500" @click="privacyAgreement">《隐私协议》</view>
      </view>
    </wd-message-box>
    <AgreementDialog
      v-if="showAgreement"
      v-model:show="showAgreement"
      @agreePrivacyLogin="handleAgreePrivacy"
    />
  </z-paging>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { setLoginPhone, getUserHasShownAgreement } from '@/utils/storage'

import { phoneCheck } from '@/interPost/login/index'
import CommonLink from '@/components/CommonLink/CommonLink.vue'
import logoImg from '@/static/images/login/logo.png'
import AgreementDialog from '@/components/agreementDiog/index.vue'

import { regPhone } from '@/utils/rule'
const { getUserIsLogin, getToken } = useUserInfo()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const message = useMessage('wd-message-box-slot')
const { aLiAuthInit } = useALiAuth()
const showAgreement = ref(false)
// 手机号
const phone = ref('')
// 是否禁用复选框
const disabledShow = ref(true)
// 复选框
const checked = ref(true)
// 状态栏
const statusBar = ref(0)
// 账户密码登录
const accountLogin = () => {
  uni.navigateTo({
    url: '/loginSetting/accountLogin/index',
  })
}
const handleChecked = () => {
  disabledShow.value = false
}
// 手机号登陆提交
const setCode = async () => {
  if (!regPhone.test(phone.value)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (checked.value && !disabledShow.value) {
    setLoginPhone(phone.value)
    phoneCheck({
      phone: phone.value,
    }).then((res: any) => {
      if (res.code === 0) {
        if (res.data.code === '0' || res.data.code === '1') {
          uni.navigateTo({
            url: '/loginSetting/verifiCode/index',
          })
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none',
            duration: 3000,
          })
        }
      }
    })
  } else {
    message
      .confirm({
        title: '',
        confirmButtonText: '同意',
        cancelButtonText: '拒绝',
      })
      .then(() => {
        disabledShow.value = false
        // 存储登陆信息
        checked.value = true
        setLoginPhone(phone.value)
        phoneCheck({
          phone: phone.value,
        }).then((res: any) => {
          if (res.code === 0) {
            if (res.data.code === '0' || res.data.code === '1') {
              uni.navigateTo({
                url: '/loginSetting/verifiCode/index',
              })
            } else {
              uni.showToast({
                title: res.msg,
                icon: 'none',
                duration: 3000,
              })
            }
          }
        })
      })
      .catch(() => {
        console.log('点击了取消按钮')
      })
  }

  // 手机号验证0
}
const uesrAgreement = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/ResumeUser',
  })
}
const privacyAgreement = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/PrivacyPolicy',
  })
}

const handleAgreePrivacy = () => {
  if (!getUserIsLogin.value) {
    if (plus?.runtime?.isAgreePrivacy()) {
      aLiAuthInit()
    }
  }
}
onMounted(() => {
  // #ifdef APP-PLUS
  if (plus.os.name === 'iOS') {
    if (!getUserHasShownAgreement()) {
      showAgreement.value = true
    }
    return
  }
  handleAgreePrivacy()
  // #endif
})
</script>

<style lang="scss" scoped>
::v-deep .wd-checkbox__label {
  margin-left: 4rpx !important;
}

::v-deep .wd-input {
  width: 100%;
  padding-left: 10rpx;
  text-align: center;
  background-color: transparent;
}

::v-deep .wd-input__placeholder {
  font-size: 36rpx !important;
}

::v-deep .wd-input__inner {
  font-size: 36rpx !important;
  color: #18181b !important;
}

.color-b {
  color: #145ad1;
}

::v-deep .wd-button__text {
  color: #000;
}

::v-deep .wd-button.is-primary {
  color: #fff;
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
}

.btn_fixed {
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    padding: 0rpx 0rpx 0rpx;
    margin-top: 30rpx;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 30rpx;
      font-size: 14px;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.logo-img {
  width: 156rpx;
  height: 156rpx;
  padding-top: 280rpx;
  padding-bottom: 70rpx;
  margin: 0rpx auto 0rpx;
  border-radius: 44rpx;
}

.login-input {
  padding-bottom: 10rpx;
  margin: 0rpx 120rpx 0rpx;
}

.login-b {
  border-bottom: 1rpx solid #d6d6d6;
}

.login-top {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-xy-constr {
  padding-top: 20rpx;
  padding-bottom: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  // padding-left: 126rpx;
  text-align: center;
}

.login-xy {
  font-weight: 400;

  .login-xy-text {
    font-size: 24rpx;
    color: #000;
    text-align: center;
  }
}

.login-container {
  // padding-top: 500rpx;

  .login-btn {
    padding: 0rpx 44rpx 0rpx;
    text-align: center;

    .login-text-nomal {
      margin: 20rpx 0rpx 0rpx;
      font-size: 28rpx;
    }
  }
}

.login-ohter {
  padding: 120rpx 140rpx 0rpx;
  color: #52525b;

  .login-ohter-icon {
    padding-top: 20rpx;
    text-align: center;
    // background-color: red;

    .t-icon {
      display: inline-block !important;
      width: 50rpx;
      height: 50rpx;
      margin: 0px 30rpx;
    }
  }
}

.login-input-l {
  font-size: 40rpx;
  font-weight: 600;
}

.textName {
  padding-bottom: 10rpx;
  color: $uni-text-color-grey;
  text-align: center;
}
</style>
